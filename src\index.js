// IL2CPP Unity game - work with native IL2CPP functions
console.log("[*] This is a Unity IL2CPP game, using native IL2CPP function hooking");

// Wait for IL2CPP module to load
function waitForIL2CPP() {
    return new Promise((resolve) => {
        const checkModule = () => {
            const il2cpp = Process.findModuleByName("libil2cpp.so");
            if (il2cpp) {
                console.log("[+] libil2cpp.so found at:", il2cpp.base);
                resolve(il2cpp);
            } else {
                setTimeout(checkModule, 100);
            }
        };
        checkModule();
    });
}

// IL2CPP function addresses from the C# class definitions
const IL2CPP_ADDRESSES = {
    // GoodyHutHelper methods
    CanCollect: 0x209D258,      // GoodyHutHelper.CanCollect()
    FinishCollect: 0x209B924,   // GoodyHutHelper.FinishCollect()
    GetRewardType: 0x209CC3C,   // GoodyHutHelper.GetRewardType()
    GetRewardAmount: 0x209D9C4, // GoodyHutHelper.GetRewardAmount()
    GetHealth: 0x209EA1C,       // GoodyHutHelper.GetHealth()
    GetCooldownTimeLeft: 0x209E9B0, // GoodyHutHelper.GetCooldownTimeLeft()
    Config: 0x209B54C,          // GoodyHutHelper.Config() - Fixed address

    // GoodyHutHelperConfig getters
    GetMaxExplorations: 0x1ED2648,  // GoodyHutHelperConfig.get_maxExplorations()
    GetNumCitizens: 0x1ED271C,      // GoodyHutHelperConfig.get_numCitizens()
    GetSellPrice: 0x1ED27F0,        // GoodyHutHelperConfig.get_sellPrice()
    GetBonusAmount: 0x1ED28C4,      // GoodyHutHelperConfig.get_bonusAmount()

    // GoodyHutHelperConfig setters
    SetMaxExplorations: 0x1ED26A8,  // GoodyHutHelperConfig.set_maxExplorations()
    SetNumCitizens: 0x1ED277C,      // GoodyHutHelperConfig.set_numCitizens()
    SetSellPrice: 0x1ED2850,        // GoodyHutHelperConfig.set_sellPrice()
    SetBonusAmount: 0x1ED2924       // GoodyHutHelperConfig.set_bonusAmount()
};

let il2cppBase = null;
let collectedInstances = new Set();

// Native IL2CPP function wrappers
function createIL2CPPFunction(address, returnType, argTypes) {
    if (!il2cppBase) return null;
    return new NativeFunction(il2cppBase.add(address), returnType, argTypes);
}

// Create native function pointers
let nativeCanCollect = null;
let nativeFinishCollect = null;
let nativeGetRewardType = null;
let nativeGetRewardAmount = null;
let nativeGetHealth = null;
let nativeGetConfig = null;

// Config-related native functions
let nativeGetMaxExplorations = null;
let nativeGetNumCitizens = null;
let nativeGetSellPrice = null;
let nativeGetBonusAmount = null;

function initializeNativeFunctions() {
    if (!il2cppBase) return false;

    try {
        // GoodyHutHelper functions
        nativeCanCollect = createIL2CPPFunction(IL2CPP_ADDRESSES.CanCollect, 'bool', ['pointer']);
        nativeFinishCollect = createIL2CPPFunction(IL2CPP_ADDRESSES.FinishCollect, 'void', ['pointer']);
        nativeGetRewardType = createIL2CPPFunction(IL2CPP_ADDRESSES.GetRewardType, 'int', ['pointer']);
        nativeGetRewardAmount = createIL2CPPFunction(IL2CPP_ADDRESSES.GetRewardAmount, 'int', ['pointer']);
        nativeGetHealth = createIL2CPPFunction(IL2CPP_ADDRESSES.GetHealth, 'int', ['pointer']);
        nativeGetConfig = createIL2CPPFunction(IL2CPP_ADDRESSES.Config, 'pointer', ['pointer']);

        // GoodyHutHelperConfig functions
        nativeGetMaxExplorations = createIL2CPPFunction(IL2CPP_ADDRESSES.GetMaxExplorations, 'int', ['pointer']);
        nativeGetNumCitizens = createIL2CPPFunction(IL2CPP_ADDRESSES.GetNumCitizens, 'int', ['pointer']);
        nativeGetSellPrice = createIL2CPPFunction(IL2CPP_ADDRESSES.GetSellPrice, 'int', ['pointer']);
        nativeGetBonusAmount = createIL2CPPFunction(IL2CPP_ADDRESSES.GetBonusAmount, 'int', ['pointer']);

        console.log("[+] Native IL2CPP functions initialized successfully");
        console.log("[+] GoodyHutHelperConfig functions loaded");
        return true;
    } catch (e) {
        console.log("[-] Failed to initialize native functions:", e);
        return false;
    }
}

// Comprehensive memory scanner with multiple detection methods
function scanForGoodyHutInstances() {
    console.log("[*] Starting comprehensive GoodyHutHelper instance scan...");
    const instances = new Set();
    let scannedAddresses = 0;
    let validationAttempts = 0;

    // Method 1: Direct memory scanning with CanCollect validation
    console.log("[*] Method 1: Direct memory scanning with function validation...");
    Process.enumerateRanges('rw-').forEach(range => {
        if (range.size < 0x1000) return; // Skip small ranges

        try {
            // Scan every 8 bytes (pointer alignment)
            for (let offset = 0; offset < range.size - 0x40; offset += 0x8) {
                const addr = range.base.add(offset);
                scannedAddresses++;

                try {
                    // Direct validation - try calling CanCollect
                    const canCollect = nativeCanCollect(addr);
                    validationAttempts++;

                    if (typeof canCollect === 'boolean' || canCollect === 0 || canCollect === 1) {
                        instances.add(addr.toString());
                        console.log(`[+] FOUND GoodyHutHelper at: ${addr} (CanCollect: ${canCollect})`);

                        // If it can be collected and auto-collect is enabled, collect it immediately
                        if ((canCollect === true || canCollect === 1) && autoCollectEnabled) {
                            try {
                                nativeFinishCollect(addr);
                                console.log(`[+] Auto-collected during scan: ${addr}`);
                            } catch (e) {
                                console.log(`[-] Auto-collect failed: ${e}`);
                            }
                        }
                    }
                } catch (e) {
                    // Not a valid GoodyHutHelper instance
                }

                // Progress indicator
                if (scannedAddresses % 50000 === 0) {
                    console.log(`[*] Scanned ${scannedAddresses} addresses, found ${instances.size} instances...`);
                }

                // Safety limit to prevent infinite scanning
                if (scannedAddresses > 500000) {
                    console.log("[*] Reached scan limit, stopping...");
                    break;
                }
            }
        } catch (e) {
            // Skip problematic ranges
        }
    });

    console.log(`[*] Scan complete: ${scannedAddresses} addresses scanned, ${validationAttempts} validations, ${instances.size} instances found`);
    return Array.from(instances).map(addr => ptr(addr));
}

// Rate-limited brute force collection - 10 collections per second
function bruteForceCollectAll() {
    console.log("[*] Starting rate-limited brute force collection (10 per second)...");

    if (!nativeCanCollect || !nativeFinishCollect) {
        console.log("[-] Native functions not initialized");
        return;
    }

    let attempts = 0;
    let collections = 0;
    let canCollectSuccesses = 0;
    let lastCollectionTime = Date.now();
    const COLLECTION_RATE_MS = 100; // 100ms = 10 per second

    console.log("[*] Collection rate: 10 per second (100ms delay between collections)");

    // Scan memory and try to collect anything that responds to CanCollect
    Process.enumerateRanges('rw-').forEach(range => {
        if (range.size < 0x1000) return;

        try {
            for (let offset = 0; offset < range.size - 0x40; offset += 0x8) {
                const addr = range.base.add(offset);
                attempts++;

                try {
                    // Try CanCollect first
                    const canCollect = nativeCanCollect(addr);
                    canCollectSuccesses++;

                    // If it can be collected, collect it with rate limiting
                    if (canCollect === true || canCollect === 1) {
                        // Rate limiting - wait if needed
                        const currentTime = Date.now();
                        const timeSinceLastCollection = currentTime - lastCollectionTime;

                        if (timeSinceLastCollection < COLLECTION_RATE_MS) {
                            const sleepTime = COLLECTION_RATE_MS - timeSinceLastCollection;
                            Thread.sleep(sleepTime / 1000); // Convert to seconds
                        }

                        try {
                            nativeFinishCollect(addr);
                            collections++;
                            lastCollectionTime = Date.now();
                            console.log(`[+] Collected #${collections} at: ${addr} (Rate: ${collections}/${Math.floor((Date.now() - lastCollectionTime + (collections * COLLECTION_RATE_MS)) / 1000)}s)`);
                        } catch (e) {
                            console.log(`[-] Collection failed at ${addr}: ${e}`);
                        }
                    }
                } catch (e) {
                    // Not a valid object, continue
                }

                // Progress indicator
                if (attempts % 50000 === 0) {
                    const elapsed = Math.floor((Date.now() - (lastCollectionTime - (collections * COLLECTION_RATE_MS))) / 1000);
                    console.log(`[*] Progress: ${attempts} attempts, ${canCollectSuccesses} valid objects, ${collections} collected in ${elapsed}s`);
                }

                // Safety limit
                if (attempts > 1000000) {
                    console.log("[*] Brute force scan limit reached");
                    break;
                }
            }
        } catch (e) {
            // Skip problematic ranges
        }
    });

    const totalTime = Math.floor((Date.now() - (lastCollectionTime - (collections * COLLECTION_RATE_MS))) / 1000);
    console.log(`[*] Brute force complete: ${collections} goody huts collected in ${totalTime}s`);
    console.log(`[*] Stats: ${canCollectSuccesses} valid objects found from ${attempts} attempts`);
    console.log(`[*] Average rate: ${collections > 0 ? Math.floor(collections / Math.max(totalTime, 1)) : 0} collections/second`);
    return collections;
}

// Fast brute force - no rate limiting (use with caution)
function fastBruteForceCollectAll() {
    console.log("[*] Starting FAST brute force collection (no rate limiting)...");
    console.log("[!] WARNING: This may overwhelm the game. Use bruteForceCollectAll() for safer collection.");

    if (!nativeCanCollect || !nativeFinishCollect) {
        console.log("[-] Native functions not initialized");
        return;
    }

    let attempts = 0;
    let collections = 0;
    let canCollectSuccesses = 0;
    const startTime = Date.now();

    // Scan memory and try to collect anything that responds to CanCollect
    Process.enumerateRanges('rw-').forEach(range => {
        if (range.size < 0x1000) return;

        try {
            for (let offset = 0; offset < range.size - 0x40; offset += 0x8) {
                const addr = range.base.add(offset);
                attempts++;

                try {
                    // Try CanCollect first
                    const canCollect = nativeCanCollect(addr);
                    canCollectSuccesses++;

                    // If it can be collected, collect it immediately
                    if (canCollect === true || canCollect === 1) {
                        try {
                            nativeFinishCollect(addr);
                            collections++;
                            if (collections % 10 === 0) {
                                console.log(`[+] Fast collected ${collections} goody huts...`);
                            }
                        } catch (e) {
                            // Collection failed, but CanCollect worked
                        }
                    }
                } catch (e) {
                    // Not a valid object, continue
                }

                // Progress and safety limits
                if (attempts % 100000 === 0) {
                    const elapsed = Math.floor((Date.now() - startTime) / 1000);
                    console.log(`[*] Fast progress: ${attempts} attempts, ${collections} collected in ${elapsed}s`);
                }

                if (attempts > 1000000) {
                    console.log("[*] Fast brute force limit reached");
                    break;
                }
            }
        } catch (e) {
            // Skip problematic ranges
        }
    });

    const totalTime = Math.floor((Date.now() - startTime) / 1000);
    console.log(`[*] Fast brute force complete: ${collections} goody huts collected in ${totalTime}s`);
    console.log(`[*] Average rate: ${collections > 0 ? Math.floor(collections / Math.max(totalTime, 1)) : 0} collections/second`);
    return collections;
}

// Optimized hook-based instance detection for large-scale collection
let detectedInstances = new Set();
let processedInstances = new Set(); // Track already processed instances
let autoCollectEnabled = false;
let hookStats = {
    canCollectCalls: 0,
    finishCollectCalls: 0,
    instancesCaptured: 0,
    autoCollections: 0,
    autoCollectionFailures: 0,
    skippedDuplicates: 0
};

// Memory management settings
const MAX_DETECTED_INSTANCES = 10000; // Limit memory usage
const CLEANUP_INTERVAL = 1000; // Clean up every 1000 detections
const LOG_INTERVAL = 100; // Log progress every 100 instances
const COLLECTION_BATCH_SIZE = 10; // Process collections in batches
let lastCleanupTime = Date.now();

function setupInstanceDetectionHooks() {
    if (!il2cppBase) return;

    console.log("[*] Setting up optimized large-scale collection hooks...");
    console.log(`[*] Configuration: Log every ${LOG_INTERVAL}, Cleanup every ${CLEANUP_INTERVAL}, Max instances: ${MAX_DETECTED_INSTANCES}`);

    // Optimized CanCollect hook for large-scale collection
    try {
        Interceptor.attach(il2cppBase.add(IL2CPP_ADDRESSES.CanCollect), {
            onEnter: function(args) {
                hookStats.canCollectCalls++;
                const instance = args[0];
                this.instance = instance;
                this.instanceStr = instance ? instance.toString() : null;

                // Skip if already processed to reduce overhead
                if (this.instanceStr && processedInstances.has(this.instanceStr)) {
                    hookStats.skippedDuplicates++;
                    this.skipProcessing = true;
                    return;
                }

                // Add to detected instances with memory management
                if (instance && !instance.isNull() && this.instanceStr) {
                    if (!detectedInstances.has(this.instanceStr)) {
                        // Memory management - prevent overflow
                        if (detectedInstances.size >= MAX_DETECTED_INSTANCES) {
                            this.performCleanup();
                        }

                        detectedInstances.add(this.instanceStr);
                        hookStats.instancesCaptured++;

                        // Reduced logging - only every LOG_INTERVAL instances
                        if (hookStats.instancesCaptured % LOG_INTERVAL === 0) {
                            console.log(`[*] Progress: ${hookStats.instancesCaptured} instances detected, ${hookStats.autoCollections} collected`);
                        }
                    }
                }
            },

            onLeave: function(retval) {
                // Skip processing if already handled
                if (this.skipProcessing) return;

                const canCollect = retval.toInt32();

                // Auto-collect if enabled and CanCollect returns 1
                if (canCollect === 1 && this.instance && this.instanceStr && autoCollectEnabled) {
                    // Mark as processed to avoid duplicate collection
                    processedInstances.add(this.instanceStr);

                    try {
                        // Rate-limited collection to prevent device overload
                        if (hookStats.autoCollections % COLLECTION_BATCH_SIZE === 0 && hookStats.autoCollections > 0) {
                            Thread.sleep(0.01); // 10ms pause every batch
                        }

                        nativeFinishCollect(this.instance);
                        hookStats.autoCollections++;

                        // Reduced logging for collections
                        if (hookStats.autoCollections % (LOG_INTERVAL / 10) === 0) {
                            console.log(`[+] Auto-collected ${hookStats.autoCollections} goody huts (${Math.floor(hookStats.autoCollections / hookStats.instancesCaptured * 100)}% success rate)`);
                        }

                    } catch (e) {
                        hookStats.autoCollectionFailures++;
                        // Only log failures occasionally to reduce spam
                        if (hookStats.autoCollectionFailures % 50 === 0) {
                            console.log(`[-] Auto-collection failures: ${hookStats.autoCollectionFailures}`);
                        }
                    }
                }
            },

            // Helper function for memory cleanup
            performCleanup: function() {
                const currentTime = Date.now();
                if (currentTime - lastCleanupTime > 5000) { // Cleanup every 5 seconds max
                    const oldSize = detectedInstances.size;

                    // Keep only the most recent instances (convert to array, slice, back to Set)
                    const instanceArray = Array.from(detectedInstances);
                    const keepCount = Math.floor(MAX_DETECTED_INSTANCES * 0.7); // Keep 70%
                    detectedInstances = new Set(instanceArray.slice(-keepCount));

                    // Also cleanup processed instances
                    const processedArray = Array.from(processedInstances);
                    const keepProcessedCount = Math.floor(processedArray.length * 0.5); // Keep 50%
                    processedInstances = new Set(processedArray.slice(-keepProcessedCount));

                    lastCleanupTime = currentTime;
                    console.log(`[*] Memory cleanup: ${oldSize} -> ${detectedInstances.size} instances, ${processedInstances.size} processed`);
                }
            }
        });
        console.log("[+] Optimized CanCollect hook installed");
    } catch (e) {
        console.log(`[-] Failed to install CanCollect hook: ${e}`);
    }

    // Minimal FinishCollect hook - just for statistics
    try {
        Interceptor.attach(il2cppBase.add(IL2CPP_ADDRESSES.FinishCollect), {
            onEnter: function(args) {
                hookStats.finishCollectCalls++;
                // Minimal logging to reduce overhead
                if (hookStats.finishCollectCalls % (LOG_INTERVAL * 2) === 0) {
                    console.log(`[*] FinishCollect calls: ${hookStats.finishCollectCalls}`);
                }
            }
        });
        console.log("[+] Minimal FinishCollect hook installed");
    } catch (e) {
        console.log(`[-] Failed to install FinishCollect hook: ${e}`);
    }

    console.log("[+] Optimized hooks setup complete - ready for large-scale collection");
}

// Enhanced statistics function for large-scale collection monitoring
function getHookStats() {
    const successRate = hookStats.instancesCaptured > 0 ?
        Math.floor(hookStats.autoCollections / hookStats.instancesCaptured * 100) : 0;
    const failureRate = hookStats.autoCollections > 0 ?
        Math.floor(hookStats.autoCollectionFailures / (hookStats.autoCollections + hookStats.autoCollectionFailures) * 100) : 0;

    console.log("[*] Large-Scale Collection Statistics:");
    console.log(`    CanCollect calls: ${hookStats.canCollectCalls.toLocaleString()}`);
    console.log(`    Instances detected: ${hookStats.instancesCaptured.toLocaleString()}`);
    console.log(`    Auto-collections: ${hookStats.autoCollections.toLocaleString()}`);
    console.log(`    Collection failures: ${hookStats.autoCollectionFailures.toLocaleString()}`);
    console.log(`    Skipped duplicates: ${hookStats.skippedDuplicates.toLocaleString()}`);
    console.log(`    Success rate: ${successRate}%`);
    console.log(`    Failure rate: ${failureRate}%`);
    console.log(`    Memory usage: ${detectedInstances.size.toLocaleString()} detected, ${processedInstances.size.toLocaleString()} processed`);
    console.log(`    FinishCollect calls: ${hookStats.finishCollectCalls.toLocaleString()}`);

    return {
        ...hookStats,
        successRate,
        failureRate,
        memoryUsage: {
            detected: detectedInstances.size,
            processed: processedInstances.size
        }
    };
}

// Function to get collection summary
function getCollectionSummary() {
    const stats = getHookStats();
    console.log("\n[*] Collection Summary:");
    console.log(`    🎯 Total Goody Huts Collected: ${stats.autoCollections.toLocaleString()}`);
    console.log(`    📊 Collection Efficiency: ${stats.successRate}%`);
    console.log(`    ⚡ Processing Speed: ${Math.floor(stats.canCollectCalls / Math.max(1, (Date.now() - lastCleanupTime) / 1000))} calls/sec`);
    console.log(`    💾 Memory Optimization: ${stats.skippedDuplicates.toLocaleString()} duplicates skipped`);

    if (stats.autoCollections > 0) {
        console.log(`    ✅ SUCCESS: Large-scale collection is working!`);
    } else if (stats.instancesCaptured > 0) {
        console.log(`    ⚠️  DETECTED: ${stats.instancesCaptured} instances found but auto-collect may be disabled`);
    } else {
        console.log(`    ❌ NO ACTIVITY: Try enableAutoCollect() and interact with goody huts`);
    }

    return stats;
}

// Enable/disable auto-collection optimized for large-scale collection
function enableAutoCollect() {
    autoCollectEnabled = true;
    console.log("[+] LARGE-SCALE AUTO-COLLECTION ENABLED!");
    console.log("[*] Optimized for 70,000+ instances with reduced logging");
    console.log("[*] Progress will be shown every 100 detections and 10 collections");
    console.log("[*] Memory management active to prevent device overload");
    console.log("[*] Rate limiting: 10ms pause every 10 collections");
    console.log("");
    console.log("📱 To start collection:");
    console.log("   1. Go in-game and tap any goody hut");
    console.log("   2. Watch for progress messages");
    console.log("   3. Use getCollectionSummary() to check progress");
    console.log("   4. Collection will happen automatically!");
}

function disableAutoCollect() {
    autoCollectEnabled = false;
    console.log("[-] Auto-collection disabled.");
    console.log(`[*] Final stats: ${hookStats.autoCollections} collected, ${hookStats.instancesCaptured} detected`);
}

// Emergency stop function for large-scale collection
function emergencyStop() {
    autoCollectEnabled = false;
    console.log("[!] EMERGENCY STOP ACTIVATED");
    console.log("[*] Auto-collection disabled immediately");
    console.log("[*] Current progress saved:");
    getCollectionSummary();
    console.log("[*] You can resume with enableAutoCollect()");
}

// Test if IL2CPP addresses are accessible
function testIL2CPPAddresses() {
    console.log("[*] Testing IL2CPP function addresses...");

    const testResults = {
        baseAddress: il2cppBase ? il2cppBase.toString() : "NOT SET",
        canCollectAddr: il2cppBase ? il2cppBase.add(IL2CPP_ADDRESSES.CanCollect).toString() : "N/A",
        finishCollectAddr: il2cppBase ? il2cppBase.add(IL2CPP_ADDRESSES.FinishCollect).toString() : "N/A",
        nativeFunctionsReady: !!(nativeCanCollect && nativeFinishCollect)
    };

    console.log("[*] Test Results:");
    console.log(`    IL2CPP Base: ${testResults.baseAddress}`);
    console.log(`    CanCollect Address: ${testResults.canCollectAddr}`);
    console.log(`    FinishCollect Address: ${testResults.finishCollectAddr}`);
    console.log(`    Native Functions Ready: ${testResults.nativeFunctionsReady}`);

    // Test if we can read memory at these addresses
    if (il2cppBase) {
        try {
            const canCollectPtr = il2cppBase.add(IL2CPP_ADDRESSES.CanCollect);
            const instruction = canCollectPtr.readU32();
            console.log(`[+] CanCollect address readable: 0x${instruction.toString(16)}`);
        } catch (e) {
            console.log(`[-] CanCollect address not readable: ${e}`);
        }

        try {
            const finishCollectPtr = il2cppBase.add(IL2CPP_ADDRESSES.FinishCollect);
            const instruction = finishCollectPtr.readU32();
            console.log(`[+] FinishCollect address readable: 0x${instruction.toString(16)}`);
        } catch (e) {
            console.log(`[-] FinishCollect address not readable: ${e}`);
        }
    }

    return testResults;
}

function getDetectedInstances() {
    console.log(`[*] Found ${detectedInstances.size} detected instances`);
    return Array.from(detectedInstances).map(addr => ptr(addr));
}

// Debug function to test addresses and function calls
function debugGoodyHutInstance(instancePtr) {
    if (!instancePtr) {
        console.log("[-] No instance pointer provided. Usage: debugGoodyHutInstance(ptr('0x6d1443bdc0'))");
        console.log("[*] Getting detected instances for you...");
        const instances = getDetectedInstances();
        if (instances.length > 0) {
            console.log(`[*] Found ${instances.length} detected instances. Debugging the first one...`);
            instancePtr = instances[0];
        } else {
            console.log("[-] No detected instances. Try enableAutoCollect() and tap a goody hut first.");
            return;
        }
    }

    console.log(`[*] Debugging GoodyHutHelper instance at: ${instancePtr}`);

    try {
        console.log("[*] Testing basic functions...");

        // Test CanCollect
        try {
            const canCollect = nativeCanCollect(instancePtr);
            console.log(`[+] CanCollect: ${canCollect}`);
        } catch (e) {
            console.log(`[-] CanCollect failed: ${e}`);
        }

        // Test GetRewardAmount
        try {
            const rewardAmount = nativeGetRewardAmount(instancePtr);
            console.log(`[+] RewardAmount: ${rewardAmount}`);
        } catch (e) {
            console.log(`[-] GetRewardAmount failed: ${e}`);
        }

        // Test GetHealth
        try {
            const health = nativeGetHealth(instancePtr);
            console.log(`[+] Health: ${health}`);
        } catch (e) {
            console.log(`[-] GetHealth failed: ${e}`);
        }

        // Test Config() - this might be the issue
        try {
            const configPtr = nativeGetConfig(instancePtr);
            console.log(`[+] Config pointer: ${configPtr}`);

            if (configPtr && !configPtr.isNull()) {
                console.log("[*] Testing config functions...");

                // Test config getters
                try {
                    const maxExp = nativeGetMaxExplorations(configPtr);
                    console.log(`[+] MaxExplorations: ${maxExp}`);
                } catch (e) {
                    console.log(`[-] GetMaxExplorations failed: ${e}`);
                }

                try {
                    const numCitizens = nativeGetNumCitizens(configPtr);
                    console.log(`[+] NumCitizens: ${numCitizens}`);
                } catch (e) {
                    console.log(`[-] GetNumCitizens failed: ${e}`);
                }
            } else {
                console.log("[-] Config pointer is null or invalid");
            }
        } catch (e) {
            console.log(`[-] Config() failed: ${e}`);
        }

    } catch (e) {
        console.log(`[-] Debug failed: ${e}`);
    }
}

// Quick debug function - automatically uses the first detected instance
function quickDebug() {
    console.log("[*] Quick debug - using first detected instance...");
    const instances = getDetectedInstances();
    if (instances.length > 0) {
        debugGoodyHutInstance(instances[0]);
    } else {
        console.log("[-] No detected instances. Try enableAutoCollect() and tap a goody hut first.");
    }
}

// Enhanced function to get detailed goody hut information
function getGoodyHutDetails(instancePtr) {
    const details = {
        address: instancePtr.toString(),
        canCollect: false,
        rewardType: 0,
        rewardAmount: 0,
        health: 0,
        config: null,
        maxExplorations: 0,
        numCitizens: 0,
        sellPrice: 0,
        bonusAmount: 0,
        valid: false
    };

    try {
        // Basic GoodyHutHelper properties
        details.canCollect = nativeCanCollect(instancePtr);
        details.rewardType = nativeGetRewardType(instancePtr);
        details.rewardAmount = nativeGetRewardAmount(instancePtr);
        details.health = nativeGetHealth(instancePtr);

        // Get config pointer
        if (nativeGetConfig) {
            const configPtr = nativeGetConfig(instancePtr);
            if (configPtr && !configPtr.isNull()) {
                details.config = configPtr.toString();

                // Get config properties
                try {
                    details.maxExplorations = nativeGetMaxExplorations(configPtr);
                    details.numCitizens = nativeGetNumCitizens(configPtr);
                    details.sellPrice = nativeGetSellPrice(configPtr);
                    details.bonusAmount = nativeGetBonusAmount(configPtr);
                } catch (e) {
                    console.log(`[-] Failed to read config properties: ${e}`);
                }
            }
        }

        details.valid = true;
    } catch (e) {
        console.log(`[-] Failed to get details for ${instancePtr}: ${e}`);
    }

    return details;
}

// Function to analyze and display goody hut information
function analyzeGoodyHut(instancePtr) {
    console.log(`[*] Analyzing goody hut at: ${instancePtr}`);
    const details = getGoodyHutDetails(instancePtr);

    if (!details.valid) {
        console.log("[-] Invalid goody hut instance");
        return details;
    }

    console.log(`[*] Can Collect: ${details.canCollect}`);
    console.log(`[*] Reward Type: ${details.rewardType}`);
    console.log(`[*] Reward Amount: ${details.rewardAmount}`);
    console.log(`[*] Health: ${details.health}`);
    console.log(`[*] Config Address: ${details.config}`);
    console.log(`[*] Max Explorations: ${details.maxExplorations}`);
    console.log(`[*] Citizens Required: ${details.numCitizens}`);
    console.log(`[*] Sell Price: ${details.sellPrice}`);
    console.log(`[*] Bonus Amount: ${details.bonusAmount}`);

    return details;
}

// Module waiting and hooking logic
function waitForModule(moduleName) {
    return new Promise(resolve => {
        const interval = setInterval(() => {
            const module = Process.findModuleByName(moduleName);
            if (module != null) {
                clearInterval(interval);
                resolve(module);
            }
        }, 300);
    });
}

async function main() {
    try {
        const il2cpp = await waitForModule("libil2cpp.so");
        console.log("[+] il2cpp.so base address:", il2cpp.base);

        // Calculate actual addresses
        const addresses = {
            CanCollect: il2cpp.base.add(0x209D258),
            FinishCollect: il2cpp.base.add(0x209B924)
        };

        // Hook CanCollect with safer implementation
        Interceptor.attach(addresses.CanCollect, {
            onEnter(args) {
                this.instance = args[0];
                console.log("[+] CanCollect called");
            },
            onLeave(retval) {
                const canCollect = retval.toInt32();
                console.log(`[*] CanCollect returned: ${canCollect}`);
                
                // Store the instance and result for manual collection
                if (canCollect === 1) {
                    console.log("[*] Collection is available!");
                    globalThis.lastCollectibleInstance = this.instance;
                }
            }
        });

        // Hook FinishCollect with minimal implementation
        Interceptor.attach(addresses.FinishCollect, {
            onEnter(args) {
                console.log("[+] FinishCollect called");
            },
            onLeave() {
                console.log("[*] FinishCollect completed");
            }
        });

        console.log("[+] Hooks installed successfully");

    } catch (e) {
        console.error("[-] Error in main:", e);
    }
}

// Enhanced collection function using correct addresses
function triggerCollection() {
    if (globalThis.lastCollectibleInstance) {
        try {
            console.log("[*] Attempting collection...");
            const finishCollect = new NativeFunction(
                Module.findModuleByName("libil2cpp.so").base.add(0x209B924), // FinishCollect address
                'void',
                ['pointer']
            );
            finishCollect(globalThis.lastCollectibleInstance);
            console.log("[+] Collection triggered successfully");
        } catch (e) {
            console.log("[-] Collection failed:", e);
        }
    } else {
        console.log("[-] No collectable instance found");
    }
}

// Function to check if collection is possible
function checkCanCollect() {
    if (globalThis.lastCollectibleInstance) {
        try {
            const canCollect = new NativeFunction(
                Module.findModuleByName("libil2cpp.so").base.add(0x209D258), // CanCollect address
                'bool',
                ['pointer']
            );
            const result = canCollect(globalThis.lastCollectibleInstance);
            console.log(`[*] CanCollect result: ${result}`);
            return result;
        } catch (e) {
            console.log("[-] CanCollect check failed:", e);
            return false;
        }
    } else {
        console.log("[-] No instance to check");
        return false;
    }
}

// Memory scanner function
function scanForGoodyHuts() {
    console.log("[*] Starting memory scan for GoodyHut instances...");
    
    Process.enumerateRanges('r--').forEach(range => {
        try {
            Memory.scan(range.base, range.size, '47 6F 6F 64 79 48 75 74', {
                onMatch(address, size) {
                    console.log('[+] Potential GoodyHut reference found at:', address);
                    console.log(hexdump(address, { length: 32 }));
                },
                onComplete() {
                    console.log('[*] Scan completed for range:', range.base);
                }
            });
        } catch (e) {
            // Ignore read errors
        }
    });
}

// Start the main function
setImmediate(main);

// Native IL2CPP collection function
function collectAllNative() {
    console.log("[*] Starting native IL2CPP collection...");

    if (!il2cppBase || !nativeCanCollect || !nativeFinishCollect) {
        console.log("[-] Native functions not initialized. Run initializeNativeFunctions() first.");
        return;
    }

    // Try multiple detection methods
    let instances = scanForGoodyHutInstances();

    // If no instances found, try detected instances from hooks
    if (instances.length === 0) {
        console.log("[*] No instances found via scanning, trying detected instances...");
        instances = getDetectedInstances();
    }

    if (instances.length === 0) {
        console.log("[-] No GoodyHutHelper instances found. Make sure you're in-game with goody huts visible.");
        console.log("[*] Try interacting with a goody hut first to trigger detection hooks.");
        return;
    }

    let collectedCount = 0;

    instances.forEach((instancePtr, index) => {
        try {
            console.log(`[*] Checking instance ${index + 1}/${instances.length}: ${instancePtr}`);

            // Check if this instance can be collected
            const canCollect = nativeCanCollect(instancePtr);
            console.log(`[*] CanCollect result: ${canCollect}`);

            if (canCollect) {
                console.log("[+] Collecting goody hut...");

                // Try to get reward info before collection
                try {
                    if (nativeGetRewardAmount && nativeGetHealth) {
                        const rewardAmount = nativeGetRewardAmount(instancePtr);
                        const health = nativeGetHealth(instancePtr);
                        console.log(`[*] Reward Amount: ${rewardAmount}, Health: ${health}`);
                    }
                } catch (e) {
                    console.log("[-] Could not get reward details:", e);
                }

                // Perform the collection
                nativeFinishCollect(instancePtr);
                collectedCount++;
                console.log(`[+] Collection ${collectedCount} completed!`);

                // Add to collected set to avoid double collection
                collectedInstances.add(instancePtr.toString());
            }
        } catch (e) {
            console.log(`[-] Error processing instance ${instancePtr}:`, e);
        }
    });

    console.log(`[*] Collection complete. Collected ${collectedCount} out of ${instances.length} goody huts.`);
}

// Simplified collection focusing on what works
function collectDetectedInstances() {
    console.log("[*] Collecting all detected instances...");

    const instances = getDetectedInstances();
    if (instances.length === 0) {
        console.log("[-] No detected instances. Interact with goody huts first to detect them.");
        return;
    }

    let collectedCount = 0;
    let failedCount = 0;

    instances.forEach((instancePtr, index) => {
        try {
            console.log(`[*] Processing instance ${index + 1}/${instances.length}: ${instancePtr}`);

            // Simple approach - just check CanCollect and collect if possible
            const canCollect = nativeCanCollect(instancePtr);
            console.log(`[*] CanCollect result: ${canCollect}`);

            if (canCollect === true || canCollect === 1) {
                console.log("[+] Attempting collection...");
                nativeFinishCollect(instancePtr);
                collectedCount++;
                console.log(`[+] Successfully collected goody hut ${collectedCount}!`);
            } else {
                console.log("[-] Cannot collect this goody hut");
                failedCount++;
            }
        } catch (e) {
            console.log(`[-] Error processing ${instancePtr}: ${e}`);
            failedCount++;
        }
    });

    console.log(`\n[*] Collection Summary:`);
    console.log(`    Successfully collected: ${collectedCount}`);
    console.log(`    Failed/Skipped: ${failedCount}`);
    console.log(`    Total processed: ${instances.length}`);
}

// Direct collection function - tries to collect all detected instances immediately
function forceCollectAll() {
    console.log("[*] Force collecting all detected instances...");

    const instances = getDetectedInstances();
    if (instances.length === 0) {
        console.log("[-] No detected instances. Try interacting with goody huts first.");
        return;
    }

    let successCount = 0;

    instances.forEach((instancePtr, index) => {
        try {
            console.log(`[*] Force collecting ${index + 1}/${instances.length}: ${instancePtr}`);

            // Skip CanCollect check, just try to collect directly
            nativeFinishCollect(instancePtr);
            successCount++;
            console.log(`[+] Force collected ${successCount}!`);

            // Small delay to avoid overwhelming the game
            Thread.sleep(0.01);
        } catch (e) {
            console.log(`[-] Force collection failed for ${instancePtr}: ${e}`);
        }
    });

    console.log(`[*] Force collection complete: ${successCount}/${instances.length} collected`);
}

// Smart collection with filtering based on config properties
function smartCollectWithFilters(options = {}) {
    const {
        minRewardAmount = 0,
        minBonusAmount = 0,
        maxCitizensRequired = 999,
        minMaxExplorations = 0
    } = options;

    console.log("[*] Starting smart collection with filters:");
    console.log(`    Min Reward: ${minRewardAmount}`);
    console.log(`    Min Bonus: ${minBonusAmount}`);
    console.log(`    Max Citizens: ${maxCitizensRequired}`);
    console.log(`    Min Explorations: ${minMaxExplorations}`);

    const instances = getDetectedInstances();
    if (instances.length === 0) {
        console.log("[-] No detected instances. Interact with goody huts first.");
        return;
    }

    let collectedCount = 0;
    let skippedCount = 0;
    let totalValue = 0;

    instances.forEach((instancePtr, index) => {
        try {
            const details = getGoodyHutDetails(instancePtr);

            if (!details.valid || !details.canCollect) {
                skippedCount++;
                return;
            }

            // Apply filters
            const passesFilters =
                details.rewardAmount >= minRewardAmount &&
                details.bonusAmount >= minBonusAmount &&
                details.numCitizens <= maxCitizensRequired &&
                details.maxExplorations >= minMaxExplorations;

            if (passesFilters) {
                console.log(`[+] Collecting filtered goody hut ${collectedCount + 1}:`);
                console.log(`    Reward: ${details.rewardAmount}, Bonus: ${details.bonusAmount}`);
                console.log(`    Citizens: ${details.numCitizens}, Explorations: ${details.maxExplorations}`);

                nativeFinishCollect(instancePtr);
                collectedCount++;
                totalValue += details.rewardAmount + details.bonusAmount;
            } else {
                console.log(`[-] Skipped (filters): R:${details.rewardAmount} B:${details.bonusAmount} C:${details.numCitizens}`);
                skippedCount++;
            }
        } catch (e) {
            console.log(`[-] Error processing ${instancePtr}:`, e);
            skippedCount++;
        }
    });

    console.log(`\n[*] Smart Collection Summary:`);
    console.log(`    Collected: ${collectedCount}`);
    console.log(`    Skipped: ${skippedCount}`);
    console.log(`    Total Value: ${totalValue}`);
}

// Enhanced mass auto-collect with detailed analysis
function massAutoCollect() {
    console.log("[*] Starting enhanced mass auto-collection...");

    if (!il2cppBase || !nativeCanCollect || !nativeFinishCollect) {
        console.log("[-] Native functions not initialized.");
        return;
    }

    let scanned = 0;
    let collected = 0;
    let totalValue = 0;
    let highValueCount = 0;

    console.log("[*] Scanning all memory ranges for GoodyHutHelper instances...");

    // Scan all readable/writable memory ranges
    Process.enumerateRanges('rw-').forEach(range => {
        if (range.size < 0x1000) return; // Skip small ranges

        try {
            // Scan every 8 bytes (pointer alignment)
            for (let offset = 0; offset < range.size - 0x40; offset += 0x8) {
                const ptr = range.base.add(offset);
                scanned++;

                try {
                    // Try calling CanCollect on this pointer
                    const canCollect = nativeCanCollect(ptr);

                    if (canCollect === true || canCollect === 1) {
                        // Get detailed information before collecting
                        const details = getGoodyHutDetails(ptr);

                        if (details.valid) {
                            const isHighValue = details.rewardAmount > 1000 || details.bonusAmount > 500;
                            if (isHighValue) highValueCount++;

                            console.log(`[+] Found ${isHighValue ? 'HIGH-VALUE' : 'standard'} goody hut at: ${ptr}`);
                            console.log(`    Reward: ${details.rewardAmount}, Bonus: ${details.bonusAmount}`);
                            console.log(`    Citizens: ${details.numCitizens}, Max Explorations: ${details.maxExplorations}`);

                            try {
                                // Collect it immediately
                                nativeFinishCollect(ptr);
                                collected++;
                                totalValue += details.rewardAmount + details.bonusAmount;
                                console.log(`[+] Successfully collected goody hut ${collected}! (Total value: ${totalValue})`);

                                // Add small delay to avoid overwhelming the game
                                Thread.sleep(0.01);
                            } catch (e) {
                                console.log(`[-] Failed to collect at ${ptr}:`, e);
                            }
                        }
                    }
                } catch (e) {
                    // Not a valid GoodyHutHelper instance, continue
                }

                // Progress indicator
                if (scanned % 10000 === 0) {
                    console.log(`[*] Progress: ${scanned} scanned, ${collected} collected (${highValueCount} high-value)...`);
                }

                // Safety limit
                if (scanned > 1000000) {
                    console.log("[*] Reached scan limit, stopping...");
                    return;
                }
            }
        } catch (e) {
            // Skip problematic ranges
        }
    });

    console.log(`\n[*] Enhanced Mass Collection Complete!`);
    console.log(`    Scanned: ${scanned} addresses`);
    console.log(`    Collected: ${collected} goody huts`);
    console.log(`    High-Value: ${highValueCount} goody huts`);
    console.log(`    Total Value: ${totalValue}`);
}

// Targeted collection using IL2CPP object enumeration
function smartAutoCollect() {
    console.log("[*] Starting smart auto-collection...");

    if (!il2cppBase) {
        console.log("[-] IL2CPP not initialized.");
        return;
    }

    let collected = 0;

    // Try to find IL2CPP objects in heap ranges
    Process.enumerateRanges('rw-').forEach(range => {
        if (range.size < 0x10000) return; // Focus on larger heap ranges

        try {
            // Look for IL2CPP object patterns
            Memory.scan(range.base, range.size, '?? ?? ?? ?? ?? ?? ?? ??', {
                onMatch: function(address) {
                    try {
                        // Check if this could be a GoodyHutHelper
                        const canCollect = nativeCanCollect(address);
                        if (canCollect) {
                            console.log(`[+] Found and collecting goody hut at: ${address}`);
                            nativeFinishCollect(address);
                            collected++;
                        }
                    } catch (e) {
                        // Not a valid instance
                    }
                },
                onComplete: function() {
                    // Range scan complete
                }
            });
        } catch (e) {
            // Skip problematic ranges
        }
    });

    console.log(`[*] Smart collection complete! Collected ${collected} goody huts.`);
}

// Initialize IL2CPP when module loads
async function initializeScript() {
    try {
        console.log("[*] Initializing IL2CPP script...");
        const il2cpp = await waitForIL2CPP();
        il2cppBase = il2cpp.base;

        if (initializeNativeFunctions()) {
            // Set up instance detection hooks
            setupInstanceDetectionHooks();

            console.log("[+] Script initialization complete!");
            console.log("[+] GoodyHutHelperConfig integration enabled!");
            console.log("");
            console.log("[*] Available functions:");
            console.log("  🔍 DIAGNOSTICS (START HERE):");
            console.log("    - testIL2CPPAddresses() - Test if addresses are working");
            console.log("    - getHookStats() - Check if hooks are being triggered");
            console.log("    - getDetectedInstances() - Show detected instances");
            console.log("");
            console.log("  🎯 COLLECTION METHODS:");
            console.log("    - enableAutoCollect() - Auto-collect when tapping goody huts");
            console.log("    - bruteForceCollectAll() - Rate-limited scan & collect (10/sec)");
            console.log("    - fastBruteForceCollectAll() - Fast collection (no rate limit)");
            console.log("    - scanForGoodyHutInstances() - Find instances via memory scan");
            console.log("    - massAutoCollect() - Enhanced mass collection");
            console.log("");
            console.log("  🔧 DEBUG & ANALYSIS:");
            console.log("    - quickDebug() - Auto-debug first detected instance");
            console.log("    - debugGoodyHutInstance(ptr('0xADDRESS')) - Test specific instance");
            console.log("");
            console.log("  🎯 TROUBLESHOOTING WORKFLOW:");
            console.log("    1. testIL2CPPAddresses() - Verify addresses work");
            console.log("    2. enableAutoCollect() + tap goody hut + getHookStats()");
            console.log("    3. If hooks don't trigger: bruteForceCollectAll()");
            console.log("    4. If nothing works: scanForGoodyHutInstances()");
            console.log("");
            console.log("  💡 RECOMMENDED: Try bruteForceCollectAll() - it doesn't need hooks!");
        } else {
            console.log("[-] Failed to initialize native functions");
        }
    } catch (e) {
        console.log("[-] Script initialization failed:", e);
    }
}

// Start initialization
setImmediate(initializeScript);

// Export enhanced functions for the console
globalThis.enableAutoCollect = enableAutoCollect;
globalThis.disableAutoCollect = disableAutoCollect;
globalThis.massAutoCollect = massAutoCollect;
globalThis.smartAutoCollect = smartAutoCollect;
globalThis.smartCollectWithFilters = smartCollectWithFilters;
globalThis.collectAllNative = collectAllNative;
globalThis.collectDetectedInstances = collectDetectedInstances;
globalThis.forceCollectAll = forceCollectAll;
globalThis.bruteForceCollectAll = bruteForceCollectAll;
globalThis.fastBruteForceCollectAll = fastBruteForceCollectAll;
globalThis.scanForGoodyHutInstances = scanForGoodyHutInstances;
globalThis.getDetectedInstances = getDetectedInstances;
globalThis.getHookStats = getHookStats;
globalThis.getCollectionSummary = getCollectionSummary;
globalThis.emergencyStop = emergencyStop;
globalThis.testIL2CPPAddresses = testIL2CPPAddresses;
globalThis.getGoodyHutDetails = getGoodyHutDetails;
globalThis.analyzeGoodyHut = analyzeGoodyHut;
globalThis.debugGoodyHutInstance = debugGoodyHutInstance;
globalThis.quickDebug = quickDebug;