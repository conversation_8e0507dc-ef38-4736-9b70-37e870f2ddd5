Java.perform(function() {
    // Hook GoodyHutHelper class
    const GoodyHutHelper = Java.use("GoodyHutHelper");

    // Hook CanCollect method
    GoodyHutHelper.CanCollect.implementation = function() {
        console.log("[+] CanCollect called");
        const canCollect = this.CanCollect();
        console.log(`[*] CanCollect returned: ${canCollect}`);
        
        // If collection is possible, automatically call FinishCollect
        if (canCollect) {
            console.log("[*] Auto-collecting goody hut...");
            this.FinishCollect();
        }
        
        return canCollect;
    };

    // Hook FinishCollect method
    GoodyHutHelper.FinishCollect.implementation = function() {
        console.log("[+] FinishCollect called");
        
        // Log reward info before collection
        const rewardType = this.GetRewardType();
        const rewardAmount = this.GetRewardAmount();
        const isFixed = this.IsFixedAmount();
        
        console.log(`[*] Reward Type: ${rewardType}`);
        console.log(`[*] Reward Amount: ${rewardAmount}`);
        console.log(`[*] Is Fixed Amount: ${isFixed}`);
        
        // Call original FinishCollect
        this.FinishCollect();
        
        console.log("[*] Collection completed");
    };

    // Optional: Monitor other relevant methods
    GoodyHutHelper.GetRewardType.implementation = function() {
        const type = this.GetRewardType();
        console.log(`[+] GetRewardType called: ${type}`);
        return type;
    };

    GoodyHutHelper.GetRewardAmount.implementation = function() {
        const amount = this.GetRewardAmount();
        console.log(`[+] GetRewardAmount called: ${amount}`);
        return amount;
    };

    GoodyHutHelper.GetHealth.implementation = function() {
        const health = this.GetHealth();
        console.log(`[+] GetHealth called: ${health}`);
        return health;
    };

    // Monitor cooldown status
    GoodyHutHelper.GetCooldownTimeLeft.implementation = function() {
        const cooldown = this.GetCooldownTimeLeft();
        console.log(`[+] Cooldown time left: ${cooldown}`);
        return cooldown;
    };

    // Native hook for the specific addresses using Interceptor
    const moduleBase = Process.findModuleByName("libil2cpp.so").base;
    
    // Hook CanCollect native implementation
    Interceptor.attach(moduleBase.add(0x209D258), {
        onEnter: function(args) {
            console.log("[+] Native CanCollect called");
        },
        onLeave: function(retval) {
            console.log(`[*] Native CanCollect returned: ${retval}`);
        }
    });

    // Hook FinishCollect native implementation
    Interceptor.attach(moduleBase.add(0x209B924), {
        onEnter: function(args) {
            console.log("[+] Native FinishCollect called");
        },
        onLeave: function(retval) {
            console.log("[*] Native FinishCollect completed");
        }
    });
});

// Helper function to force collection if available
function forceCollect() {
    Java.perform(function() {
        Java.choose("GoodyHutHelper", {
            onMatch: function(instance) {
                if (instance.CanCollect()) {
                    console.log("[*] Found collectable goody hut, forcing collection...");
                    instance.FinishCollect();
                }
            },
            onComplete: function() {
                console.log("[*] Finished scanning for collectable goody huts");
            }
        });
    });
}

// You can call forceCollect() from the Frida console to trigger collection